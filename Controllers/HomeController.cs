﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using SimpleRoutingApp.Models;

namespace SimpleRoutingApp.Controllers
{
    public class HomeController : Controller
    {
        // Static list to simulate database storage
        private static List<Car> cars = new List<Car>
        {
            new Car { CarId = 1, CarBrand = "Toyota", CarModel = "Corolla", CarDescription = "A compact sedan with reliable fuel efficiency.", CarPrice = 850000 },
            new Car { CarId = 2, CarBrand = "Honda", CarModel = "Civic", CarDescription = "Sporty sedan known for its modern design.", CarPrice = 1200000 },
            new Car { CarId = 3, CarBrand = "Ford", CarModel = "Mustang", CarDescription = "Iconic sports car with powerful engine options.", CarPrice = 2500000 },
            new Car { CarId = 4, CarBrand = "Nissan", CarModel = "Altima", CarDescription = "Mid-size sedan with a sleek and comfortable ride.", CarPrice = 1400000 },
            new Car { CarId = 5, CarBrand = "BMW", CarModel = "X5", CarDescription = "Luxury SUV with cutting-edge technology.", CarPrice = 3000000 },
            new Car { CarId = 6, CarBrand = "Chevrolet", CarModel = "Silverado", CarDescription = "Full-size pickup truck with a robust build.", CarPrice = 1800000 },
            new Car { CarId = 7, CarBrand = "Mercedes-Benz", CarModel = "C-Class", CarDescription = "Elegant sedan with premium features and style.", CarPrice = 2800000 },
            new Car { CarId = 8, CarBrand = "Audi", CarModel = "A4", CarDescription = "Sedan with a blend of performance and luxury.", CarPrice = 2700000 },
            new Car { CarId = 9, CarBrand = "Hyundai", CarModel = "Tucson", CarDescription = "Compact SUV offering excellent value for money.", CarPrice = 1150000 },
            new Car { CarId = 10, CarBrand = "Kia", CarModel = "Sorento", CarDescription = "Versatile SUV with a spacious interior.", CarPrice = 1450000 }
        };

        public ActionResult Index()
        {
            return View(cars);
        }

        public ActionResult AddCar()
        {
            return View();
        }

        [HttpPost]
        public ActionResult AddCar(Car car)
        {
            if (ModelState.IsValid)
            {
                // Generate new ID
                car.CarId = cars.Any() ? cars.Max(c => c.CarId) + 1 : 1;
                cars.Add(car);
                return RedirectToAction("Index");
            }
            return View(car);
        }

        public ActionResult CarDetails(int id)
        {
            var car = cars.FirstOrDefault(c => c.CarId == id);
            if (car == null)
            {
                return HttpNotFound();
            }
            return View(car);
        }

        public ActionResult About()
        {
            ViewBag.Message = "Your application description page.";
            return View();
        }

        public ActionResult Contact()
        {
            ViewBag.Message = "Your contact page.";
            return View();
        }
    }
}