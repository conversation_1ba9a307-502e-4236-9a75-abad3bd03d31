.student-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.student-title {
    color: #2c3e50;
    text-align: center;
    margin-bottom: 30px;
    font-size: 2.5rem;
    font-weight: 300;
    border-bottom: 3px solid #343a40;
    padding-bottom: 10px;
}

.student-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
}

.student-table thead {
    background: linear-gradient(135deg, #343a40, #495057);
    color: white;
}

.student-table th {
    padding: 15px 20px;
    text-align: left;
    font-weight: 600;
    font-size: 1.1rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    border: none;
}

.student-table th:first-child {
    width: 80px;
    text-align: center;
}

.student-table th:nth-child(2) {
    width: 60%;
}

.student-table th:last-child {
    width: auto;
}

.student-table tbody tr {
    transition: all 0.3s ease;
    border-bottom: 1px solid #ecf0f1;
}

.student-table tbody tr:hover {
    background-color: #f8f9fa;
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.student-table tbody tr:nth-child(even) {
    background-color: #f8f9fa;
}

.student-table tbody tr:nth-child(even):hover {
    background-color: #e9ecef;
}

.student-table td {
    padding: 15px 20px;
    border: none;
    vertical-align: middle;
}

.student-table td:first-child {
    text-align: center;
    font-weight: bold;
    color: #343a40;
    font-size: 1.1rem;
}

.student-table td:nth-child(2) {
    font-weight: 500;
    color: #2c3e50;
}

.student-table td:last-child {
    color: #7f8c8d;
    font-style: italic;
}

/* code for responsive design */
@media (max-width: 768px) {
    .student-container {
        padding: 10px;
    }
    
    .student-title {
        font-size: 2rem;
        margin-bottom: 20px;
    }
    
    .student-table {
        font-size: 0.9rem;
    }
    
    .student-table th,
    .student-table td {
        padding: 10px 15px;
    }
    
    .student-table th:first-child {
        width: 60px;
    }
    
    .student-table th:nth-child(2) {
        width: 50%;
    }
}

@media (max-width: 480px) {
    .student-table {
        font-size: 0.8rem;
    }
    
    .student-table th,
    .student-table td {
        padding: 8px 10px;
    }
    
    .student-table th:first-child {
        width: 50px;
    }
    
    .student-table th:nth-child(2) {
        width: 40%;
    }
}

/* code for animation for table rows */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.student-table tbody tr {
    animation: fadeInUp 0.6s ease forwards;
}

.student-table tbody tr:nth-child(1) { animation-delay: 0.1s; }
.student-table tbody tr:nth-child(2) { animation-delay: 0.2s; }
.student-table tbody tr:nth-child(3) { animation-delay: 0.3s; }
.student-table tbody tr:nth-child(4) { animation-delay: 0.4s; }
.student-table tbody tr:nth-child(5) { animation-delay: 0.5s; }
.student-table tbody tr:nth-child(6) { animation-delay: 0.6s; }
.student-table tbody tr:nth-child(7) { animation-delay: 0.7s; }
.student-table tbody tr:nth-child(8) { animation-delay: 0.8s; }
.student-table tbody tr:nth-child(9) { animation-delay: 0.9s; }
.student-table tbody tr:nth-child(10) { animation-delay: 1.0s; } 