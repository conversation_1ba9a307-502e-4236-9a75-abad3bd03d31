﻿@model IEnumerable<ListOfCars.Models.Car>
@{
    ViewBag.Title = "List of Cars";
}

<div class="container-fluid">
    <div class="row mb-3">
        <div class="col">
            <h1>List of Cars</h1>
        </div>
        <div class="col-auto">
            @Html.ActionLink("Add a Car", "AddCar", "Home", null, new { @class = "btn btn-primary" })
        </div>
    </div>

    <div class="table-responsive">
        <table class="table table-striped table-bordered">
            <thead class="table-dark">
                <tr>
                    <th>Car ID</th>
                    <th>Car Brand</th>
                    <th>Car Model</th>
                    <th>Car Description</th>
                    <th>Car Price (PHP)</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                @foreach (var car in Model)
                {
                    <tr>
                        <td>@car.CarId</td>
                        <td>@car.CarBrand</td>
                        <td>@car.CarModel</td>
                        <td>@car.CarDescription</td>
                        <td>₱@car.CarPrice.ToString("N0")</td>
                        <td>
                            @Html.ActionLink("View Details", "CarDetails", "Home", new { id = car.CarId }, new { @class = "btn btn-sm btn-info" })
                        </td>
                    </tr>
                }
            </tbody>
        </table>
    </div>

    @if (!Model.Any())
    {
        <div class="alert alert-info text-center">
            <h4>No cars available</h4>
            <p>@Html.ActionLink("Add the first car", "AddCar", "Home", null, new { @class = "btn btn-primary" })</p>
        </div>
    }
</div>