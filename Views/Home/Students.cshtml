﻿
@{
    ViewBag.Title = "Students";
}

@section Styles {
    <link href="~/Styles/students.css" rel="stylesheet" />
}

<div class="student-container">
    <h2 class="student-title">Student List</h2>
    <table class="student-table">
        <thead>
            <tr>
                <th>No.</th>
                <th>Name</th>
                <th>Course</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>01</td>
                <td>Alegano, Chrysta Lynn</td>
                <td>Information Technology</td>
            </tr>

            <tr>
                <td>02</td>
                <td><PERSON><PERSON>, <PERSON></td>
                <td>Information Technology</td>
            </tr>

            <tr>
                <td>03</td>
                <td>Begonia, Gennievieve C.</td>
                <td>Information Technology</td>
            </tr>

            <tr>
                <td>04</td>
                <td>Biong Aleck Christian C.</td>
                <td>Information Technology</td>
            </tr>

            <tr>
                <td>05</td>
                <td><PERSON>, <PERSON></td>
                <td>Information Technology</td>
            </tr>

            <tr>
                <td>06</td>
                <td>Guion, Dave B.</td>
                <td>Information Technology</td>
            </tr>

            <tr>
                <td>07</td>
                <td>Lopez, Diana Paula R.</td>
                <td>Information Technology</td>
            </tr>

            <tr>
                <td>08</td>
                <td>Oñas Aeon Flux</td>
                <td>Information Technology</td>
            </tr>

            <tr>
                <td>09</td>
                <td>Sanico, Ronan M.</td>
                <td>Information Technology</td>
            </tr>

            <tr>
                <td>10</td>
                <td>Uyson, Harley MIguel Q.</td>
                <td>Information Technology</td>
            </tr>
        </tbody>
    </table>
</div>