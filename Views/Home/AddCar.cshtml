@model SimpleRoutingApp.Models.Car
@{
    ViewBag.Title = "Add a Car";
}

<div class="container">
    <div class="row">
        <div class="col-md-6">
            <h2>Add a Car</h2>
            
            @using (Html.BeginForm("AddCar", "Home", FormMethod.Post, new { @class = "form" }))
            {
                @Html.AntiForgeryToken()
                @Html.ValidationSummary(true, "", new { @class = "text-danger" })

                <div class="form-group mb-3">
                    @Html.LabelFor(model => model.CarBrand, new { @class = "form-label" })
                    @Html.TextBoxFor(model => model.CarBrand, new { @class = "form-control", placeholder = "Toyota" })
                    @Html.ValidationMessageFor(model => model.CarBrand, "", new { @class = "text-danger" })
                </div>

                <div class="form-group mb-3">
                    @Html.LabelFor(model => model.CarModel, new { @class = "form-label" })
                    @Html.TextBoxFor(model => model.CarModel, new { @class = "form-control", placeholder = "Corolla" })
                    @Html.ValidationMessageFor(model => model.CarModel, "", new { @class = "text-danger" })
                </div>

                <div class="form-group mb-3">
                    @Html.LabelFor(model => model.CarDescription, new { @class = "form-label" })
                    @Html.TextAreaFor(model => model.CarDescription, new { @class = "form-control", rows = "3", placeholder = "A compact sedan with reliable fuel efficiency" })
                    @Html.ValidationMessageFor(model => model.CarDescription, "", new { @class = "text-danger" })
                </div>

                <div class="form-group mb-3">
                    @Html.LabelFor(model => model.CarPrice, new { @class = "form-label" })
                    @Html.TextBoxFor(model => model.CarPrice, new { @class = "form-control", placeholder = "850000", type = "number", step = "0.01" })
                    @Html.ValidationMessageFor(model => model.CarPrice, "", new { @class = "text-danger" })
                </div>

                <div class="form-group">
                    <button type="submit" class="btn btn-primary me-2">Save</button>
                    @Html.ActionLink("Cancel", "Index", "Home", null, new { @class = "btn btn-secondary" })
                </div>
            }
        </div>
        
        <div class="col-md-6">
            <h2>Car Details</h2>
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Preview</h5>
                    <p><strong>Car ID:</strong> <span id="preview-id">Auto-generated</span></p>
                    <p><strong>Car Brand:</strong> <span id="preview-brand">-</span></p>
                    <p><strong>Car Model:</strong> <span id="preview-model">-</span></p>
                    <p><strong>Car Description:</strong> <span id="preview-description">-</span></p>
                    <p><strong>Car Price (PHP):</strong> ₱<span id="preview-price">0</span></p>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @Scripts.Render("~/bundles/jqueryval")
    <script>
        $(document).ready(function() {
            // Live preview functionality
            $('#CarBrand').on('input', function() {
                $('#preview-brand').text($(this).val() || '-');
            });
            
            $('#CarModel').on('input', function() {
                $('#preview-model').text($(this).val() || '-');
            });
            
            $('#CarDescription').on('input', function() {
                $('#preview-description').text($(this).val() || '-');
            });
            
            $('#CarPrice').on('input', function() {
                var price = $(this).val();
                if (price && !isNaN(price)) {
                    $('#preview-price').text(parseFloat(price).toLocaleString());
                } else {
                    $('#preview-price').text('0');
                }
            });
        });
    </script>
}
