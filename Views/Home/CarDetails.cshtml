@model ListOfCars.Models.Car
@{
    ViewBag.Title = "Car Details";
}

<div class="container">
    <div class="row">
        <div class="col-md-8">
            <h2>Car Details</h2>
            
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">@Model.CarBrand @Model.CarModel</h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>Car ID:</strong> @Model.CarId</p>
                            <p><strong>Car Brand:</strong> @Model.CarBrand</p>
                            <p><strong>Car Model:</strong> @Model.CarModel</p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>Car Description:</strong></p>
                            <p class="text-muted">@Model.CarDescription</p>
                            <p><strong>Car Price (PHP):</strong></p>
                            <p class="h4 text-success">₱@Model.CarPrice.ToString("N0")</p>
                        </div>
                    </div>
                </div>
                <div class="card-footer">
                    @Html.ActionLink("← Back to List", "Index", "Home", null, new { @class = "btn btn-secondary" })
                    @Html.ActionLink("Add Another Car", "AddCar", "Home", null, new { @class = "btn btn-primary" })
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        @Html.ActionLink("View All Cars", "Index", "Home", null, new { @class = "btn btn-outline-primary" })
                        @Html.ActionLink("Add New Car", "AddCar", "Home", null, new { @class = "btn btn-outline-success" })
                    </div>
                </div>
            </div>
            
            <div class="card mt-3">
                <div class="card-header">
                    <h5 class="mb-0">Car Information</h5>
                </div>
                <div class="card-body">
                    <small class="text-muted">
                        This car information is displayed from our car database. 
                        All prices are in Philippine Peso (PHP).
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>
