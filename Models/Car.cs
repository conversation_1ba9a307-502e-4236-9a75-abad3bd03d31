using System;
using System.ComponentModel.DataAnnotations;

namespace ListOfCars.Models
{
    public class Car
    {
        public int CarId { get; set; }

        [Required(ErrorMessage = "Car Brand is required")]
        [Display(Name = "Car Brand")]
        public string Car<PERSON>rand { get; set; }

        [Required(ErrorMessage = "Car Model is required")]
        [Display(Name = "Car Model")]
        public string CarModel { get; set; }

        [Required(ErrorMessage = "Car Description is required")]
        [Display(Name = "Car Description")]
        public string CarDescription { get; set; }

        [Required(ErrorMessage = "Car Price is required")]
        [Display(Name = "Car Price (PHP)")]
        [Range(0, double.MaxValue, ErrorMessage = "Price must be a positive number")]
        public decimal CarPrice { get; set; }
    }
}
